作者：零一猴子
链接：https://www.zhihu.com/question/1919478868361842758/answer/1922284284066104523
来源：知乎
著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。

最开始我也是抗拒的。<img src="https://pic1.zhimg.com/50/v2-65a839cd4b6fda26e4fa000236983f08_720w.jpg?source=2c26e567" data-caption="" data-size="normal" data-rawwidth="660" data-rawheight="355" data-original-token="v2-d94a04ad667a1d90d477515c8805f4f1" data-default-watermark-src="https://pic1.zhimg.com/50/v2-f096f0c19205d67b48adba0ca24be2b2_720w.jpg?source=2c26e567" class="origin_image zh-lightbox-thumb" width="660" data-original="https://pica.zhimg.com/v2-65a839cd4b6fda26e4fa000236983f08_r.jpg?source=2c26e567"/>原因有三：我担心 AI 用多了，自己的编码能力会下降我觉得 AI 代码没有我写得好（可维护性、可读性、可扩展性），特别是复杂项目我担心同事 Diss 我，在 code review 的时候发现我用 AI 写代码，说我偷懒但我也不是完全排斥，该装的插件我也装，偶尔用一下自动补全，写写样板代码。这个时候，AI 生成的代码可能占我总代码量的 10% - 20%。直到我加入了一家做 AI 产品的创业公司，他们和我讲，公司 60% 的代码都是 AI 生成的。我心想，那代码质量得有多差？入职后，我花了几天时间看代码库，发现和我想的不太一样。他们的服务端项目是一个 DDD（领域驱动设计）的目录结构，每个领域目录下面，是按照传统分层设计的，比如 controller、service、dao、model 等。然后每个子目录下面，都有一个 README 文件，最外层也有一个 README 文件，都是专门给 AI 看的。里面描述了这个模块（目录）是干什么的，新增一个功能，代码应该怎么加。然后他们的工作流是这样的。有一个新的需求，他们直接和 AI 对话，说明需求，然后让 AI 参考 README 生成业务代码的框架，再通过插件（比如 Cline、Trae AI）直接应用到本地。具体业务逻辑的实现，如果不太复杂，也是和 AI 对话，让它生成代码并应用到本地，复杂的业务逻辑就自己写。<img src="https://picx.zhimg.com/50/v2-9b5a8cbd95cbda05cb9dfa0a73e1ce13_720w.jpg?source=2c26e567" data-size="normal" data-rawwidth="1440" data-rawheight="545" data-original-token="v2-4249f9e806d97151d27d2bc65e2ba87a" data-default-watermark-src="https://pic1.zhimg.com/50/v2-0a75c020c88109ddc0f6e74ece479893_720w.jpg?source=2c26e567" class="origin_image zh-lightbox-thumb" width="1440" data-original="https://pic1.zhimg.com/v2-9b5a8cbd95cbda05cb9dfa0a73e1ce13_r.jpg?source=2c26e567"/>一个例子代码的质量，也比我想象得好，虽然也会出现生成错误代码的情况，但我可以改呀。算下来，真的有 60% 左右的代码都是 AI 生成的。这有点颠覆我的认知了。我之前是会用代码补全，但很少直接让 AI 帮我生成大段的代码。因为公司鼓励大家用 AI 写代码，所以担心同事 diss 我这个理由不成立了。我也开始学他们，更多地使用 AI 帮我写代码。一段时间后，我发现用 AI 写代码是真的爽。<img src="https://picx.zhimg.com/50/v2-308f9dfa69ef2754799a51a7fc2e0fcd_720w.jpg?source=2c26e567" data-caption="" data-size="normal" data-rawwidth="988" data-rawheight="650" data-original-token="v2-39864c193f35b14c827b8cb8608d5b90" data-default-watermark-src="https://picx.zhimg.com/50/v2-1f3e89c5fead60610eed883e8e87e086_720w.jpg?source=2c26e567" class="origin_image zh-lightbox-thumb" width="988" data-original="https://picx.zhimg.com/v2-308f9dfa69ef2754799a51a7fc2e0fcd_r.jpg?source=2c26e567"/>首先，用 AI 写代码不是不思考了。如果想让他写出可维护性好、逻辑正确的代码，你在给它描述业务逻辑的时候，就要很清晰，甚至要把你写代码的思路告诉它。比如和它说定义一个 map 来维护一个配置，保证可扩展性。这就破除了我的第一个担忧：AI 用多了，自己的编码能力会下降。AI 改变的是生产方式，但怎么生产，仍然需要你把关。其次，现在 AI 产出的代码质量确实不错。如果你能控制它生成代码的粒度，准确率也会有一定提升。比如不要一下子让它生成一大段逻辑，而是按照你写代码的思路，生成一个一个的函数，然后让 AI 或者你自己来组装最终的逻辑。对于复杂项目尤是如此，你要知道还是你在把控项目， AI 只是一个工具，或者是你的员工，在帮你做体力工作。回到开头的三个阻碍我深度使用 AI 的原因：我担心 AI 用多了，自己的编码能力会下降工具平权不等于质量平权，就算未来大家都用 AI 写代码，写出来的质量也不会一样。该学的设计原则、设计模式、重构技巧还是要学，自己要知道什么样的代码是可维护性好、可读性强、方便扩展的。这样你才能评估 AI 代码的质量，并着手让 AI 或者自己去修改和调整。2. 我觉得 AI 代码没有我写得好（可维护性、可读性、可扩展性），特别是复杂项目我自己用下来，对于我不熟悉的领域（比如某个算法或者语言），AI 写的代码比我写的要好多了。怎么让 AI 写出好代码，很依赖你使用它的方式，以及你自己的编码和设计能力。3. 我担心同事 Diss 我，在 code review 的时候发现我用 AI 写代码，说我偷懒只要你保证 AI 生成的代码，或者你基于 AI 的代码修改后的结果，满足可读性、可维护性、可扩展性、高性能、边界安全等条件，谁还敢 Diss 你。又能写出好代码，花的时间还少，别人只能羡慕你，加入你。所以，放心大胆地把 AI 用起来。


昨天去某厂做了场Cursor入门分享（由于他们本身也有相关业务，所以这里不方便公开名字），这场分享的核心观点总结起来就三点：

1、AI编程有很多局限，包括但不限于“抽盲盒”、“偏科”、“健忘症”、“消息滞后”；

2、几乎所有AI编程工具都是围绕着这些局限去打造功能；

3、用好Cursor的关键，就是善用Cursor的功能来削弱这些局限。


之所以单独整理这篇文章，一方面是作为自己的复盘，补充自己在分享中可能遗漏的一些内容，另一方面是把这场分享的一些观点分享给大家，希望对大家也有启发。

关于AI编程局限的观点，最初萌芽于我的这篇文章>>>AI编程的使用场景、局限，以及我们的应对（其实大家可以不看这篇文章，因为核心观点都在这里了）。

后来随着对Cursor更深度的使用，以及对AI编程更深入的了解，我对AI编程慢慢有了个初步的认知框架。

这次线下分享，是限定主题，要讲Cursor基础入门，要相对系统。由于我不想只是简单地把所有功能罗列一遍（这也太无趣了），我更希望帮助大家构建起对Cursor或者说对AI编程的整体认知，这样不论是当前已有的功能，还是未来推出的新功能，都可以基于这个认知框架去理解。

后面我想到了之前那篇文章，想起自己在小报童社群、在知识星球老说到的一些观点，然后很多以前飘着的零散的想法，一下子串联起来了。

1、AI编程的局限，很多时候是LLMs（大语言模型）的本身局限带来的，包括但不限于生成的随机性、上下文限制、训练数据有大众小众之分、训练数据有截止时间等。

2、LLMs的这些局限，有些在AI编程中是被放大的。

3、比如AI生成的随机性（大模型是根据概率在预测下一个token），这种随机性放在AI写作上（非法律、医学等行业）一般不会有太大问题，即使错了一两个字，我们一般也能快速理解作者要表达的意思。但放到AI编程里，这其实很致命，因为代码本身是非常结构化的，但凡少了个大括号，逗号，整段代码可能都没法运行，

4、又比如AI是“偏科”的。因为AI的表现能力是基于训练它的数据，对于一些大众的编程语言如Python、JavaScript，AI往往会表现的比较出色，但对于一些小众的编程语言，则可能不尽人意。就拿Claude和Gemini来说，虽然它们都是顶尖编程模型，但由于它们没有太多微信小程序的代码训练数据，在这类项目上的表现往往没那么出色。


5、再比如“消息滞后”。每个大模型的训练语料都是截至到某个时间点的，如果你不给模型接各种外部工具、数据，那么它能回答的消息是不会超过这个时间点。而在编程领域，一些技术、框架、语言版本的更新是非常迅速的，如果训练数据过时，AI可能会给你生成不兼容的代码，甚至是不安全、有漏洞的代码。

6、除了在AI编程中会被放大的局限，还有一些局限可能没被放大，但目前很难彻底解决，典型如上下文。

7、无论哪个大模型，目前都有上下文局限，区别在于这个上下文的大小。这个上下文局限其实和我们人类的健忘很像，比如你现在看到文章的第7点，可能已经忘记第1点说什么了，你待会看完文章，可能又会遗忘现在第7点的部分内容，这就是上下文局限。放到AI编程里，大家经常把这种情况调侃为工具的降智。之前分享过一篇关于Cursor降智的文章>>>Cursor降智？盘点4种可能原因及解决方法，其中有一点谈的就是上下文限制。


8、由于LLMs本身的这些局限无法彻底解决，所以目前Cursor、Trae、Cline等AI编程工具，都是围绕着这些局限去打造功能的。还有一些第三方工具，也在为克服上述的一个或多个局限推出相关产品。

9、对于代码生成的随机性，这些AI编程工具推出了Rules功能，通过给AI“立规范”来降低这种随机性。之前分享过多篇 Cursor Rules 的文章：

cursor教程 | 如何根据不同项目写好一份合格的cursorrules?

.cursorrules将被移除，大家现在就可以迁移使用Project Rules，控制代码更精准

Cursor Rules在实际开发中的三种层级&实际应用（附20个常用Rules）

10、为了进一步降低生成随机性，也为了更好地应对上下文局限，这些AI编程工具还提供了丰富的 Context（上下文）工具，以Cursor为例，它目前提供了10多种 Context 工具，包括但不限于@codebase、@file、@folder、@git 、@terminal、@web、@Cursor Rules（没错，Rules本身就是一种context）等。

11、在这些 Context 工具中，有一些是专门应对“消息滞后”局限的，比如@web 可以实现联网搜索，@docs 可以实现阅读外部文档，还有目前大火的MCP，理论上可以接入所有你可获取的外部工具/数据。关于MCP，可以看之前这篇文章>>>关于A2A、MCP、Agent、LLM的6条极简事实，比较简洁地介绍了MCP和目前LLMs、Agent的关系。

12、由于当前的 Context 工具并不能满足所有人的需求，一些第三方平台基于自己的洞察，结合自身业务，也推出了一些小工具来提升解决“消息滞后”问题的效率，比如之前分享的 Context7 以及它推出的MCP，可以自动获取最新的技术文档，而不需要我们手动复制粘贴。之前在这篇文章也介绍过Context7的用法>>>Cursor官方下场谈Cursor正确用法，以及我的实践解读

13、上述工具往往还可以组合起来使用，比如之前这篇文章>>>Windsurf v1.8.2最值得关注的更新：自定义工作流，介绍的是Windsurf怎么通过 Rules + Workflows + Memories 的方法让代码生成更可控。还有这篇文章>>>Cursor解决bug总在绕圈？可以尝试引入 Memory Bank，介绍的就是怎么通过Rules或自定义模式来建立Memory Bank，后续可以通过@file、@folder来快速理解项目，来克服AI的“健忘症”。

14、这让我想到最近很火的MCP项目——OpenMemory MCP，其实也是在做克服AI“健忘症”的事情，只不过解决的方向更加细分，是让不同工具之间（如Cursor、Claude Desktop、Windsurf、Cline）共享上下文信息。


15、可以预想到的是，接下来还会涌现出更多类似 Context7、OpenMemory 的实用工具，用于解决当前AI编程某个细分场景下的问题。Cursor、Trae、Cline这类AI编程工具也会继续围绕前面提到的局限去发力，推出更多新能力去降低这些局限带来的影响。

16、而作为工具使用的我们，只要知道“所有的AI编程工具都是围绕这些局限打造功能的”，那么无论使用哪一款AI编程工具，都可以快速上手。

以上就是我这次线下分享的核心内容以及延展补充，希望对大家有启发。

发布于 2025-05-15 14:22・广东




早上看了Claude官方对Cursor团队的最新采访，非常精彩！

对谈话题从Cursor的崛起到AI编程的瓶颈，从Cursor新功能Background Agent到大型代码库的挑战.....其中最精彩的部分，是Cursor团队用Cursor构建Cursor（不是套娃 ）

下面整理分享我自己的收获，但强烈推荐大家去看原视频（按下图标题去搜即可）。


1、Cursor是模型能力跳跃式提升的产物。Cursor的发展有两个重要转折点，它的前身是 Gmail writing Plugin，产品在 ChatGPT 出来后马上转型为 AI IDE，这是第一个转折点；第二个转折点是 Claude 3.5 Sonnet 发布，让Cursor从之前只支持 Tab、Command+K、单文件编辑，一下子实现多文件编辑，也让Cursor迅速进入用户和资本的视野。

2、如果把Cursor现在支持的所有AI编辑工具比作一条光谱，那么光谱从左到右依次是：Tab、Command+K、Agent、Background Agent。使用Tab时，你知道你在做什么，你可以完全掌控代码，继续往光谱的右侧，你对代码的熟悉程度是依次递减的，但AI编辑工具的智能程度依次提升。


3、Background Agent的智能程度有多高呢？对比Agent模式，你不用担心Background Agent把你的本地环境搞坏或者误删什么文件，因为它拥有自己独立的运行环境，其中包含所有开发者工具，所有VS Code插件.....所有这一切，Background Agent都可以自己调用。它基本能帮你完成90%的任务，剩下的10%就由我们自己接手。

4、Background Agent接下来会是趋势。目前Cursor和Augment Code，都在这个方向上发力。之前在知识星球分享过Background Agent的应用场景，包括但不限于集成第三方工具进行协作、并行处理多个开发任务，以及将需要长时间运行的任务搬到云端进行异步执行。


5、但Background Agent并不是万能的，它目前同样面临着代码审查这个瓶颈。因为完整的软件工程并不只有代码编写这一环节，即使AI编程工具快速完成了代码编写，但如果没有解决代码验证审查，你实际上就没有真正加快软件工程的速度。

6、为了解决代码审查的瓶颈，Cursor团队在采访中表示正在想办法让它变得更简单，一个正在发生的事实就是，Cursor在v1.0大版本上线了 BugBot 自动审查代码的功能。据Cursor团队介绍，他们内部已经使用 BugBot 几个月了，已经证明它在发现细微错误方面非常有价值。之前有一期内容专门分享过，这里就不赘述了，感兴趣的小伙伴可以移步查看>>>Cursor v1.0两大亮点：新增BugBot自动审查代码、MCP一键安装

7、不过让Agent代码审查存在一个难题，就是“怎么确信Agent所做的更改是正确的，因为正确本身很模糊”，在大型代码库中解决起来尤为困难。所以Cursor做了一系列动作，包括但不限于推出Cursor Rules、整合各种Context工具等等。之前官方还专门分享过一篇指南>>>Cursor官方指南：如何在Cursor中处理大型代码库？（附个人实践解读）

8、你能想象，以上提到的所有Cursor能力，都是Cursor团队用Cursor来迭代升级的。Cursor创始人Aman表示，“我们用Cursor解决我们自己的问题，并找出我们在解决问题时遇到的问题，从而让Cursor变得更好。”他们会让团队每个人都使用Cursor，尝试为产品添加新功能，然后从内部看看这些功能是如何被使用的，以及收集了什么样的反馈。

9、在Aman看来，将内部团队作为Cursor产品的客户，是他们能够快速开发新功能的重要原因，“因为我们能非常诚实地评估它是否实用，而不必将其发送给用户，就能跟踪人们在决定是否要推进某个功能之前是如何使用它的，我认为这能加速循环。”这让我想起自己以前团队的一些产品经理，他们自己都不用自己的产品，却在大谈产品上新功能应该是怎样的。

10、采访末尾，Lukas提出了一个开放式问题，“2027年1月1年，距离现在还有不到两年，你认为有多少百分比的代码将被AI生成，随之而来的是，你的一天会是什么样子？”Cursor团队的Jacob（在Cursor负责ML机器学习）给出的回答很有意思，“这就像在1995年问一个律师，未来有多少法律文件会由Word生成一样。答案是100%。同样的，AI将会参与几乎所有代码的编写。” 这个比例，据Aman介绍，在Cursor内部，可能已经超过90%了。

11、当AI接替了所有的代码生成，我们作为开发者的角色还有存在的意义吗？当然有，你要做的是指导代码的执行方向，但这需要你有良好的taste。taste有天生的，也可以通过后天学习培养，但不论哪种，它在未来会越来越重要。

12、关于taste，我之前在这篇文章>>>AI设计、AI编程普及后，我们最稀缺的三种能力 就分享过，“AI创作门槛一定会越来越低，AIGC作品也会越来越多，未来更加珍稀、更加令人印象深刻的作品，一定是有更多人类经验，也就是今天提到的taste在里面的。” 如果你有taste，还有超强的行动力，你将在这个AI时代如鱼得水。

以上就是我二刷这场采访的收获，希望对大家也有启发 ~

发布于 2025-06-13 23:41・广东

MCP Feedback Enhanced
:open_book: 项目背景故事
大家好！我是一名后端程序猿，想跟大家分享一个我最近在维护的开源项目。
这是我第一次在这发帖，还请多多指教。

一开始我只是看到了 interactive-feedback-mcp 这个项目，觉得很有趣。但是因为我本身有非常多的项目需要 SSH remote 进行开发，原版只有本地 GUI 接口对我来说不太够用。

于是我就自己 fork 了这个项目，一边改一边调整，加了 Web UI 支持、多语言接口、图片上载等功能。突然有一天注意到星星数在增加，搜了一下才知道被分享到了 这里！

没想到像是发现了新大陆，原来 linux.do 有这么棒的开发者社群！以后有机会会多多在这里分享交流。

:bullseye: 项目介绍
MCP Feedback Enhanced - 建立反馈导向的开发工作流程，提供Web UI 和桌面应用程序双重选择，完美适配本地、SSH Remote 环境（Cursor SSH Remote、VS Code Remote SSH）与 WSL (Windows Subsystem for Linux) 环境。通过引导 AI 与用户确认而非进行推测性操作，可将多次工具调用合并为单次反馈导向请求，大幅节省平台成本并提升开发效率。

GitHub
Stars
Forks

github.com

GitHub - Minidoracat/mcp-feedback-enhanced: Enhanced MCP server for interactive user feedback...
Enhanced MCP server for interactive user feedback and command execution in AI-assisted development, featuring dual interface support (Web UI and Desktop Application) with intelligent environment detection and cross-platform compatibility.

:glowing_star: 主要功能
:desktop_computer: 双重界面支持
桌面应用程序：基于 Tauri 的跨平台原生应用，支持 Windows、macOS、Linux
Web UI 界面：轻量级浏览器界面，适合远程和 WSL 环境
环境自动检测：智能识别 SSH Remote、WSL 等特殊环境
统一功能体验：两种界面提供完全相同的功能
:memo: 智能工作流程
提示词管理：常用提示词的 CRUD 操作、使用统计、智能排序
自动定时提交：1-86400 秒弹性计时器，支持暂停、恢复、取消，新增暂停/开始按钮控制
自动执行命令（v2.6.0）：新建会话和提交后可自动执行预设命令，提升开发效率
会话管理追踪：本地文件存储、隐私控制、历史导出（支持 JSON、CSV、Markdown 格式）、即时统计、弹性超时设定
连接监控：WebSocket 状态监控、自动重连、品质指示
AI 工作摘要 Markdown 显示：支持丰富的 Markdown 语法渲染，包含标题、粗体、代码区块、列表、链接等格式，提升内容可读性
:artist_palette: 现代化体验
响应式设计：适配不同屏幕尺寸，模块化 JavaScript 架构
音效通知：内建多种音效、支持自定义音效上传、音量控制
系统通知（v2.6.0）：重要事件（如自动提交、会话超时等）的系统级即时提醒
智能记忆：输入框高度记忆、一键复制、设定持久化
多语言支持：简体中文、英文、繁体中文，即时切换
:framed_picture: 图片与媒体
全格式支持：PNG、JPG、JPEG、GIF、BMP、WebP
便捷上传：拖拽文件、剪贴板粘贴（Ctrl+V）
无限制处理：支持任意大小图片，自动智能处理
:rocket: 快速开始
# 安装 uv（如果还没有的话）
pip install uv

:hammer_and_wrench: 配置 MCP
基本配置（适合大多数用户）：

{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    }
  }
}
进阶配置（需要自定义环境）：

{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
桌面应用程序配置（v2.5.0 新功能 - 使用原生桌面应用程序）：

{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
:bug: 常见问题:
Q: SSH Remote 环境下浏览器无法启动或无法访问
A: 提供两种解决方案：

方案一：环境变量设置（v2.5.5 推荐）
在 MCP 配置中设置 "MCP_WEB_HOST": "0.0.0.0" 允许远程访问：

{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_WEB_HOST": "0.0.0.0",
        "MCP_WEB_PORT": "8765"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
然后在本地浏览器打开：http://[远程主机IP]:8765

方案二：SSH 端口转发（传统方法）

使用默认配置（MCP_WEB_HOST: 127.0.0.1）
设置 SSH 端口转发：
VS Code Remote SSH: 按 Ctrl+Shift+P → “Forward a Port” → 输入 8765
Cursor SSH Remote: 手动添加端口转发规则（端口 8765）
在本地浏览器打开：http://localhost:8765
详细解决方案请参考：SSH Remote 环境使用指南