# 结构化提示词框架教程

## 🎯 课程概述

结构化提示词框架是业内广泛认可的提示词设计范式，已被百度、智谱、字节等主流大模型厂商列为推荐标准，并登上GitHub全球趋势排行榜前十。

## 📚 目录

- [1. 什么是结构化提示词框架](#1-什么是结构化提示词框架)
- [2. AI对话思维框架](#2-ai对话思维框架)
- [3. 表达方法论基础](#3-表达方法论基础)
- [4. 结构化提示词设计](#4-结构化提示词设计)
- [5. 实战案例分析](#5-实战案例分析)
- [6. 课后作业](#6-课后作业)

---

## 1. 什么是结构化提示词框架

### 🔍 核心理念
结构化提示词框架将**写提示词**变成**像写文章一样**，通过标题、子标题、段落等结构化思想，让复杂的提示词变成简单的填空题。

### 🌟 主要优势
- **标准化**: 业内广泛认可的写作范式
- **模块化**: 可自由添加和删减模块
- **可复用**: 一次编写，多次使用
- **高效性**: 把写作文变成填空题

### 💡 设计原理
```
传统提示词: 一段连续文本，难以管理
结构化提示词: 模块化设计，清晰易懂
```

---

## 2. AI对话思维框架

### 🚀 BROK框架

**国内流行框架**，由陈才猫猫总提出：

#### 框架结构
```
B - Background (背景): 为AI提供充足信息
R - Role (角色): 定义AI扮演的角色
O - Objective (目标): 明确期望的结果
K - Key Result (关键结果): 具体要什么结果
```

#### 改进方法
1. **改进输入**: 优化背景、角色、目标设置
2. **改进答案**: 在对话中指正AI输出
3. **多次生成**: 同样提示词多次生成，优中选优

#### 实战案例：短剧剧本创作
```markdown
## Background (背景)
抖音爆款微短剧基础信息：
- 单集时长：30秒-15分钟
- 明确主题和主线
- 快节奏，强冲突

## Role (角色)
你是一位具有丰富经验的微短剧剧本创作专家

## Objective (目标)
生成详细的微短剧剧本，包括标题、大纲、分集剧本

## Key Result (关键结果)
- 剧本梗概
- 角色设定
- 剧本大纲
- 分集剧本内容
```

### 🌍 CRISP框架

**国外流行框架**，适合复杂任务：

#### 框架结构
```
C - Capability (能力): 定义角色能力
R - Role (角色): 明确扮演角色
I - Insight (洞察): 背景信息和上下文
S - Statement (指令): 具体要求
P - Personality (个性): 回答风格和方式
```

#### 示例应用
```markdown
作为机器学习框架软件开发专家和博客撰写专家，
面向技术人员读者群，需要全面介绍机器学习框架，
包含真实案例研究，使用指定写作风格...
```

---

## 3. 表达方法论基础

### 📊 六何分析法 (5W1H)

**经典表达方法论**：

#### 基础要素
- **What (什么)**: 要做什么事，大小目标
- **Why (为什么)**: 原因和背景信息
- **Where (哪里)**: 地点、使用工具、联网能力
- **When (何时)**: 工作流程，第一步、第二步
- **Who (谁)**: 角色扮演，如何做事
- **How (如何)**: 方法、工具、模型

#### AI提问应用
```
向AI提问时考虑：
- 要做什么事？目标是什么？
- 为什么要做？背景信息？
- 在哪里做？使用什么工具？
- 什么时候做？工作流程？
- 谁来做？扮演什么角色？
- 如何做？使用什么方法？
```

### 🎯 其他高效模型

#### STAR模型
- **S - Situation**: 背景情况
- **T - Task**: 具体任务
- **A - Action**: 采取行动
- **R - Result**: 达成结果

#### SMART模型
- **S - Specific**: 明确具体
- **M - Measurable**: 可衡量
- **A - Achievable**: 可达成
- **R - Relevant**: 相关性
- **T - Time-bound**: 时间限制

---

## 4. 结构化提示词设计

### 🏗️ 经典框架结构

#### 基础模块
```markdown
# Role (角色)
定义AI扮演的角色

## Profile (档案)
- 语言: 中文/英文
- 作者: 角色创建者
- 技能: 擅长的专业技能

## Rules (规则)
- 回答生成的约束规则
- 输出格式要求
- 注意事项

## Workflow (工作流)
1. 第一步：具体操作
2. 第二步：具体操作
3. 第三步：具体操作

## Initialization (初始化)
按照角色设定，严格遵循规则，
根据工作流执行任务
```

### 🔧 技巧融合

结构化提示词整合了多种技巧：
- **角色扮演法**: Role模块
- **任务分解**: Workflow模块
- **格式化输出**: Rules模块
- **细节描述**: Profile模块

### 💡 模块化优势
- **自由组合**: 根据需求选择模块
- **灵活调整**: 可增删任意模块
- **标准化**: 统一的写作格式
- **可复用**: 模板化设计

---

## 5. 实战案例分析

### 📊 案例一：竞品分析专家

#### 业务场景
天天乐公司推出菠萝百香果饮品，需要进行竞品分析，了解市场竞争对手情况。

#### 角色设计
```markdown
# Role: 竞品分析专家

## Profile
- 描述: 经验丰富，擅长对不同产品进行深入市场分析
- 专长: 明确产品定位和竞争策略
- 技能: 
  - 熟悉奶茶饮品市场
  - 了解主要竞争对手
  - 掌握SWOT分析方法

## Background
菠萝百香果是一款饮品，目标受众为年轻消费群体

## Objective
对菠萝百香果进行竞品分析，明确产品定位和受众

## Rules
- 分析内容全面详细
- 覆盖市场所有表现
- 遵循专业竞品分析流程

## Case (参考模板)
[提供竞品分析报告模板格式]

## Workflow
1. 市场数据收集
2. 对比分析产品差异
3. 调查目标受众偏好
4. 市场表现分析
5. 制定竞争策略
6. 生成完整报告
```

#### 执行效果
AI按照工作流逐步执行：
1. **数据收集**: 获取蜜雪冰城、茶百道等竞品信息
2. **对比分析**: 分析口感、功能差异点
3. **受众调研**: 设计调查问卷，分析偏好
4. **市场分析**: 评估竞争对手表现
5. **策略制定**: 提供市场推广建议
6. **报告生成**: 输出完整竞品分析报告

### 🎯 案例二：面试出题专家

#### 应用场景
求职者需要练习面试，HR需要生成面试题目。

#### 角色设计
```markdown
# Role: 面试出题大师

## Profile
- 描述: 经验丰富的HR专家和面试官
- 专长: 设计针对性面试题目
- 技能: 了解各行业岗位要求

## Objective
为指定岗位生成高质量面试题目

## Rules
- 题目数量: 30道
- 专业技能: 80%
- 情境问题: 10%
- 行为面试: 10%

## Workflow
1. 接收岗位信息
2. 分析岗位要求
3. 设计题目结构
4. 生成面试题目
5. 分类整理输出
```

#### 执行结果
- **Python开发工程师**: 生成30道专业面试题
- **题目分类**: 按技能、情境、行为分类
- **难度适中**: 可根据需求调整难度
- **即时可用**: 直接用于面试准备

---

## 6. 课后作业

### 📝 作业要求

#### 任务一：理论学习
1. **了解表达方法论**
   - 与AI对话，了解表达的重要性
   - 学习表达方法论都有哪些
   - 理解结构化语言表达的意义

2. **框架拆解分析**
   - 拆解本课程提到的结构化提示词
   - 分析各个模块的共性和差异
   - 总结其中的逻辑关系

#### 任务二：实践应用
1. **选择应用场景**
   - 选择你的工作或学习中的实际需求
   - 设计对应的角色（如：文案专家、数据分析师等）

2. **编写结构化提示词**
   ```markdown
   使用框架模板：
   - Role: 定义角色
   - Profile: 完善角色档案
   - Rules: 设定规则约束
   - Workflow: 设计工作流程
   - Initialization: 初始化设置
   ```

3. **测试和优化**
   - 在AI工具中测试效果
   - 根据结果优化提示词
   - 记录改进过程

#### 任务三：智能体封装
1. **模板化设计**
   - 将测试成功的提示词模板化
   - 设计可复用的参数配置

2. **效果评估**
   - 对比结构化前后的效果差异
   - 分析提升的具体方面

### 🎯 提交格式
```
作业文件夹/
├── 理论学习总结.md
├── 实践案例/
│   ├── 角色设计.md
│   ├── 测试对话截图/
│   └── 优化记录.md
└── 效果评估报告.md
```

### 📊 评分标准
- **理论掌握** (30%): 对框架概念的理解深度
- **实践应用** (40%): 结构化提示词的设计质量
- **效果评估** (20%): 对比分析的客观性
- **创新性** (10%): 在框架基础上的创新尝试

---

## 📚 总结

### 🎯 核心要点
1. **结构化思维**: 像写文章一样写提示词
2. **模块化设计**: 可自由组合的框架结构
3. **方法论融合**: 整合多种表达和思维方法
4. **实战导向**: 通过实际案例验证效果

### 🚀 学习成果
通过本课程学习，您将：
- 掌握业内标准的提示词写作范式
- 学会设计可复用的结构化模板
- 提高AI交互的效率和准确性
- 具备解决复杂任务的框架思维

### 🔄 下一步
在接下来的课程中，将详细讲解：
- 如何设计每个模块的具体内容
- 不同行业场景的模板应用
- 智能体封装和部署方法
- 高级技巧和优化策略

---

*💡 记住：结构化提示词框架是所有方法论的封装，让AI交互变得更加高效和可控！* 