# Cursor AI编程指南 - 多模态应用开发

## 🚀 Cursor简介

Cursor是一款基于VSCode的AI编程助手，特别适合开发多模态AI应用。它能理解代码上下文，提供智能补全和重构建议。

## 📚 核心功能

### 1. AI聊天助手 (Ctrl+L)
与AI进行对话，讨论设计思路和技术方案。

#### 多模态项目规划示例
```
提问: "我想开发一个图像标记工具，结合多模态AI进行分析，应该如何设计架构？"

AI回答: 建议分为以下模块：
1. 图像预处理模块
2. 标记引擎
3. AI接口层
4. 结果展示模块
```

### 2. 代码生成 (Ctrl+K)
基于自然语言描述生成代码。

#### 示例：生成图像标记函数
```python
# 输入描述：创建一个函数，在图像上添加带序号的标记点
# AI生成代码：
def add_numbered_markers(image, points, color=(255, 0, 0)):
    """
    在图像上添加带序号的标记点
    
    Args:
        image: 输入图像
        points: 标记点坐标列表 [(x1,y1), (x2,y2), ...]
        color: 标记颜色 (B, G, R)
    
    Returns:
        marked_image: 添加标记后的图像
    """
    import cv2
    marked_image = image.copy()
    
    for i, (x, y) in enumerate(points):
        # 绘制圆形标记
        cv2.circle(marked_image, (x, y), 10, color, -1)
        # 添加序号文字
        cv2.putText(marked_image, str(i+1), (x-5, y+5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    return marked_image
```

### 3. 行内编辑 (Ctrl+I)
选中代码片段进行AI辅助修改。

#### 代码优化示例
```python
# 原代码（选中后使用Ctrl+I）
def process_image(img):
    result = cv2.imread(img)
    return result

# AI优化后
def process_image(img_path, color_mode='color'):
    """
    加载和预处理图像
    
    Args:
        img_path: 图像文件路径
        color_mode: 'color', 'grayscale', 'rgba'
    
    Returns:
        processed_image: 预处理后的图像
    """
    if color_mode == 'grayscale':
        result = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
    elif color_mode == 'rgba':
        result = cv2.imread(img_path, cv2.IMREAD_UNCHANGED)
    else:
        result = cv2.imread(img_path, cv2.IMREAD_COLOR)
    
    if result is None:
        raise FileNotFoundError(f"无法加载图像: {img_path}")
    
    return result
```

## 🛠️ 多模态项目开发流程

### 1. 项目初始化
```bash
# 创建项目目录
mkdir multimodal-ai-app
cd multimodal-ai-app

# 初始化Python项目
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 使用Cursor打开项目
cursor .
```

### 2. 依赖管理
使用Cursor AI生成requirements.txt：

```
# 在空文件中输入注释，然后Ctrl+K
# 生成多模态AI项目的依赖列表

# AI生成的requirements.txt
opencv-python==********
pillow==10.0.1
numpy==1.24.3
matplotlib==3.7.2
transformers==4.35.0
torch==2.1.0
openai==1.3.5
requests==2.31.0
streamlit==1.28.1
gradio==3.50.2
```

### 3. 项目结构生成
```python
# 使用Ctrl+L询问AI项目结构建议
"""
建议的项目结构：

multimodal-ai-app/
├── src/
│   ├── image_processing/
│   │   ├── __init__.py
│   │   ├── markers.py        # 标记功能
│   │   └── preprocessor.py   # 图像预处理
│   ├── ai_interface/
│   │   ├── __init__.py
│   │   ├── openai_client.py  # OpenAI接口
│   │   └── local_models.py   # 本地模型
│   ├── ui/
│   │   ├── __init__.py
│   │   ├── streamlit_app.py  # Web界面
│   │   └── gradio_demo.py    # 演示界面
│   └── utils/
│       ├── __init__.py
│       └── file_handler.py   # 文件处理
├── tests/
├── docs/
├── requirements.txt
└── README.md
"""
```

### 4. 核心功能开发

#### 图像标记模块 (src/image_processing/markers.py)
```python
# 使用Ctrl+K生成标记功能模块
import cv2
import numpy as np
from typing import List, Tuple, Optional

class ImageMarker:
    """图像标记工具类"""
    
    def __init__(self, marker_color: Tuple[int, int, int] = (0, 255, 0)):
        self.marker_color = marker_color
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.7
        self.thickness = 2
    
    def add_numbered_points(self, image: np.ndarray, 
                          points: List[Tuple[int, int]]) -> np.ndarray:
        """添加带序号的点标记"""
        marked_image = image.copy()
        
        for i, (x, y) in enumerate(points):
            # 绘制圆形标记
            cv2.circle(marked_image, (x, y), 8, self.marker_color, -1)
            cv2.circle(marked_image, (x, y), 12, (255, 255, 255), 2)
            
            # 添加序号
            text = str(i + 1)
            text_size = cv2.getTextSize(text, self.font, self.font_scale, self.thickness)[0]
            text_x = x - text_size[0] // 2
            text_y = y + text_size[1] // 2
            
            cv2.putText(marked_image, text, (text_x, text_y),
                       self.font, self.font_scale, (255, 255, 255), self.thickness)
        
        return marked_image
    
    def add_bounding_boxes(self, image: np.ndarray,
                          boxes: List[Tuple[int, int, int, int]],
                          labels: Optional[List[str]] = None) -> np.ndarray:
        """添加边界框标记"""
        marked_image = image.copy()
        
        for i, (x, y, w, h) in enumerate(boxes):
            # 绘制边界框
            cv2.rectangle(marked_image, (x, y), (x + w, y + h), 
                         self.marker_color, 2)
            
            # 添加标签
            if labels and i < len(labels):
                label = labels[i]
                label_size = cv2.getTextSize(label, self.font, self.font_scale, self.thickness)[0]
                cv2.rectangle(marked_image, (x, y - label_size[1] - 10),
                             (x + label_size[0], y), self.marker_color, -1)
                cv2.putText(marked_image, label, (x, y - 5),
                           self.font, self.font_scale, (255, 255, 255), self.thickness)
        
        return marked_image
```

#### AI接口模块 (src/ai_interface/openai_client.py)
```python
# 使用Ctrl+K生成OpenAI接口
import openai
import base64
from typing import Dict, Any, Optional
import json

class MultimodalAIClient:
    """多模态AI客户端"""
    
    def __init__(self, api_key: str, model: str = "gpt-4-vision-preview"):
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model
    
    def encode_image(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def analyze_marked_image(self, 
                           image_path: str,
                           prompt: str,
                           max_tokens: int = 500) -> Dict[str, Any]:
        """分析标记后的图像"""
        
        base64_image = self.encode_image(image_path)
        
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=max_tokens
        )
        
        return {
            "analysis": response.choices[0].message.content,
            "usage": response.usage._asdict(),
            "model": self.model
        }
    
    def batch_analyze_images(self, 
                           image_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """批量分析图像"""
        results = []
        
        for data in image_data:
            try:
                result = self.analyze_marked_image(
                    data['image_path'], 
                    data['prompt']
                )
                result['image_id'] = data.get('id', 'unknown')
                results.append(result)
            except Exception as e:
                results.append({
                    'image_id': data.get('id', 'unknown'),
                    'error': str(e)
                })
        
        return results
```

### 5. Web界面开发

#### Streamlit应用 (src/ui/streamlit_app.py)
```python
# 使用Ctrl+K生成Streamlit Web应用
import streamlit as st
import cv2
import numpy as np
from PIL import Image
import tempfile
import os
from src.image_processing.markers import ImageMarker
from src.ai_interface.openai_client import MultimodalAIClient

def main():
    st.title("🌊 多模态AI图像分析工具")
    st.markdown("基于标记提示法的智能图像分析平台")
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 配置选项")
        
        # API配置
        api_key = st.text_input("OpenAI API Key", type="password")
        model_choice = st.selectbox("选择模型", 
                                   ["gpt-4-vision-preview", "gpt-4-turbo"])
        
        # 标记配置
        st.subheader("🎯 标记设置")
        marker_color = st.color_picker("标记颜色", "#00FF00")
        enable_numbering = st.checkbox("显示序号", True)
        enable_boxes = st.checkbox("显示边界框", False)
    
    # 主界面
    uploaded_file = st.file_uploader("📁 上传图像", 
                                    type=['png', 'jpg', 'jpeg'])
    
    if uploaded_file is not None:
        # 显示原图
        image = Image.open(uploaded_file)
        st.image(image, caption="原始图像", use_column_width=True)
        
        # 图像标记功能
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🎯 添加标记")
            
            if st.button("自动检测对象"):
                # 这里可以集成目标检测模型
                st.info("正在检测对象...")
                # TODO: 实现自动检测逻辑
            
            # 手动添加标记点
            num_points = st.number_input("标记点数量", min_value=0, max_value=20, value=0)
            points = []
            
            for i in range(num_points):
                col_x, col_y = st.columns(2)
                with col_x:
                    x = st.number_input(f"点{i+1} X坐标", min_value=0, 
                                       max_value=image.width, value=0, key=f"x_{i}")
                with col_y:
                    y = st.number_input(f"点{i+1} Y坐标", min_value=0, 
                                       max_value=image.height, value=0, key=f"y_{i}")
                points.append((x, y))
        
        with col2:
            st.subheader("🖼️ 标记预览")
            
            if points:
                # 创建标记图像
                img_array = np.array(image)
                marker = ImageMarker(marker_color=tuple(int(marker_color[i:i+2], 16) 
                                                       for i in (1, 3, 5)))
                
                if enable_numbering:
                    marked_img = marker.add_numbered_points(img_array, points)
                    st.image(marked_img, caption="标记后图像", use_column_width=True)
        
        # AI分析部分
        if api_key and points:
            st.subheader("🤖 AI分析")
            
            analysis_prompt = st.text_area("分析提示词", 
                value="请分析图像中标记的对象，描述每个标记点对应的内容和特征。")
            
            if st.button("开始分析"):
                with st.spinner("AI正在分析图像..."):
                    try:
                        # 保存标记后的图像到临时文件
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
                            marked_image_pil = Image.fromarray(marked_img)
                            marked_image_pil.save(tmp_file.name)
                            
                            # 调用AI分析
                            client = MultimodalAIClient(api_key, model_choice)
                            result = client.analyze_marked_image(tmp_file.name, analysis_prompt)
                            
                            # 显示结果
                            st.success("分析完成！")
                            st.write("### 📋 分析结果")
                            st.write(result['analysis'])
                            
                            # 显示使用统计
                            with st.expander("📊 API使用统计"):
                                st.json(result['usage'])
                        
                        # 清理临时文件
                        os.unlink(tmp_file.name)
                        
                    except Exception as e:
                        st.error(f"分析失败: {str(e)}")

if __name__ == "__main__":
    main()
```

## 🔧 Cursor高级技巧

### 1. 代码重构
选中函数或类，使用Ctrl+K进行重构：
```
"将这个函数拆分为更小的模块，提高可读性和可维护性"
```

### 2. 文档生成
```python
# 选中函数，使用Ctrl+I生成文档
def complex_function(param1, param2):
    # AI自动生成的文档：
    """
    执行复杂的图像处理操作
    
    Args:
        param1 (type): 参数1的描述
        param2 (type): 参数2的描述
    
    Returns:
        type: 返回值描述
    
    Raises:
        Exception: 异常情况描述
    """
    pass
```

### 3. 测试用例生成
```python
# 使用Ctrl+K为函数生成测试用例
def test_image_marker():
    """AI生成的测试用例"""
    import pytest
    import numpy as np
    
    # 创建测试图像
    test_image = np.zeros((100, 100, 3), dtype=np.uint8)
    marker = ImageMarker()
    
    # 测试添加标记点
    points = [(20, 30), (50, 60)]
    result = marker.add_numbered_points(test_image, points)
    
    # 验证结果
    assert result.shape == test_image.shape
    assert not np.array_equal(result, test_image)  # 确保图像被修改
```

## 📝 最佳实践

### 1. 项目组织
- 使用模块化设计
- 每个功能独立成类或函数
- 保持代码整洁和可读性

### 2. AI助手使用
- 详细描述需求，获得更好的代码生成效果
- 多次迭代优化，不要指望一次完美
- 结合自己的判断，AI建议不一定总是最佳

### 3. 代码质量
- 使用类型提示增强代码可读性
- 添加适当的错误处理
- 编写单元测试确保代码质量

## 🔗 相关资源

### Cursor学习资料
- [Cursor官方文档](https://docs.cursor.com/)
- [Cursor快捷键大全](https://docs.cursor.com/get-started/migrate-from-vscode#keyboard-shortcuts)
- [AI编程最佳实践](https://docs.cursor.com/advanced/tips)

### 多模态开发资源
- [OpenAI Vision API文档](https://platform.openai.com/docs/guides/vision)
- [Streamlit组件库](https://streamlit.io/components)
- [计算机视觉实战项目](https://github.com/topics/computer-vision)

---

*🚀 利用Cursor的AI能力，让多模态应用开发变得更加高效和有趣！* 