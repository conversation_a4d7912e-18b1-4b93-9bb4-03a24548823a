# 02-核心方法

这个目录包含最重要的提示词核心方法，是提升AI对话质量的关键技能。

## 📚 内容目录

### 🔍 [细节描述法.md](./细节描述法.md)
**重要程度**: ⭐⭐⭐⭐⭐  
**适合场景**: 所有AI对话场景  
**学习时间**: 45-60分钟  
**内容概览**:
- 什么是有信息量的表达
- 如何提供有效的背景信息
- 实战案例：论文撰写、营销方案
- 避免信息过载的技巧

### 🎭 [角色扮演法.md](./角色扮演法.md)
**重要程度**: ⭐⭐⭐⭐  
**适合场景**: 需要专业建议的情况  
**学习时间**: 30-40分钟  
**内容概览**:
- 如何让AI扮演专业角色
- 角色设定的关键要素
- 不同场景的角色选择策略
- 提升回答专业性的技巧

### 🧩 [复杂问题分解法.md](./复杂问题分解法.md)
**重要程度**: ⭐⭐⭐⭐  
**适合场景**: 复杂任务和多步骤问题  
**学习时间**: 40-50分钟  
**内容概览**:
- 如何拆解复杂问题
- 分步骤引导AI思考
- 提升问题解决效率
- 实际案例分析

## 🎯 学习建议

### 📈 建议学习顺序
1. **细节描述法** - 最基础也最重要的方法
2. **角色扮演法** - 提升回答专业性
3. **复杂问题分解法** - 处理复杂任务

### 🎪 实践建议
- 每学完一个方法，立即找一个实际场景练习
- 对比使用方法前后的AI回答质量差异
- 记录自己的使用心得和改进点

## 🔗 与其他章节的关系

- **基础理论**: 为这些方法提供了理论基础
- **实战技巧**: 是这些核心方法的具体应用
- **工具指南**: 在具体工具中应用这些方法

## ⏭️ 下一步学习

掌握核心方法后，建议继续学习：
- [03-实战技巧](../03-实战技巧/) - 学习具体的应用技巧
- [04-工具指南](../04-工具指南/) - 在实际工具中应用方法 